{"version": 3, "file": "real_case.js", "sources": ["pages/real_case/real_case.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVhbF9jYXNlL3JlYWxfY2FzZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"real-case-container\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">调解成功案例</view>\n\t\t\t<text class=\"header-text\">以下是部分真实调解成功的案例，供您参考</text>\n\t\t</view>\n\t\t<button @click=\"navigateTo()\">测</button>\n\t\t<!-- 搜索和筛选区域 -->\n\t\t<!-- <view class=\"search-filter-section\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<uni-easyinput\n\t\t\t\t\tv-model=\"searchKeyword\"\n\t\t\t\t\tplaceholder=\"搜索案例关键词\"\n\t\t\t\t\tsuffixIcon=\"search\"\n\t\t\t\t\t:clearable=\"true\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t\t@iconClick=\"handleSearch\"\n\t\t\t\t></uni-easyinput>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"filter-tabs\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"{ active: activeFilter === item.value }\"\n\t\t\t\t\tv-for=\"item in filterOptions\" \n\t\t\t\t\t:key=\"item.value\"\n\t\t\t\t\t@click=\"handleFilterChange(item.value)\"\n\t\t\t\t>\n\t\t\t\t\t{{ item.label }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view> -->\n\n\t\t<!-- 案例列表 -->\n\t\t<view class=\"case-list\">\n\t\t\t<view \n\t\t\t\tclass=\"case-card\" \n\t\t\t\tv-for=\"caseItem in filteredCases\" \n\t\t\t\t:key=\"caseItem.id\"\n\t\t\t\t@click=\"navigateToDetail(caseItem.id)\"\n\t\t\t>\n\t\t\t\t<!-- 案例头部 -->\n\t\t\t\t<view class=\"case-header\">\n\t\t\t\t\t<view class=\"case-title\">{{ caseItem.title }}</view>\n\t\t\t\t\t<view class=\"case-date\">{{ caseItem.date }}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 案例标签 -->\n\t\t\t\t<!-- <view class=\"case-tags\">\n\t\t\t\t\t<text class=\"case-tag type-tag\">{{ caseItem.type }}</text>\n\t\t\t\t\t<text class=\"case-tag status-tag\" :class=\"getStatusClass(caseItem.status)\">\n\t\t\t\t\t\t{{ caseItem.status }}\n\t\t\t\t\t</text>\n\t\t\t\t</view> -->\n\n\t\t\t\t<!-- 金额信息 -->\n\t\t\t\t<view class=\"amount-info\">\n\t\t\t\t\t<view class=\"amount-row\">\n\t\t\t\t\t\t<text class=\"amount-label\">债务金额：</text>\n\t\t\t\t\t\t<text class=\"debt-amount\">¥{{ formatAmount(caseItem.debtAmount) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"amount-row\">\n\t\t\t\t\t\t<text class=\"amount-label\">调解结果：</text>\n\t\t\t\t\t\t<!-- ¥{{ formatAmount(caseItem.resolvedAmount) }} -->\n\t\t\t\t\t\t<text class=\"resolved-amount\">{{ caseItem.reductionRate }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view class=\"reduction-info\">\n\t\t\t\t\t\t<text class=\"reduction-rate\">{{ caseItem.reductionRate }}</text>\n\t\t\t\t\t</view> -->\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 案例简介 -->\n\t\t\t\t<view class=\"case-summary\">\n\t\t\t\t\t<text class=\"summary-text\">{{ caseItem.summary }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 查看详情按钮 -->\n\t\t\t\t<view class=\"detail-button\">\n\t\t\t\t\t<text class=\"button-text\">查看详情</text>\n\t\t\t\t\t<text class=\"arrow-icon\">›</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-if=\"filteredCases.length === 0 && !loading\">\n\t\t\t<text class=\"empty-text\">暂无相关案例</text>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-state\" v-if=\"loading\">\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n// 导入API工具类\nimport { api } from '@/utils/api.js';\n\n// 响应式数据\nconst searchKeyword = ref('');\nconst activeFilter = ref('all');\nconst loading = ref(false);\nconst caseList = ref([]);\n\n// 筛选选项\nconst filterOptions = [\n\t{ label: '全部', value: 'all' },\n\t{ label: '信用卡', value: 'credit_card' },\n\t{ label: '车贷', value: 'car_loan' },\n\t{ label: '房贷', value: 'mortgage' },\n\t{ label: '其他', value: 'other' }\n];\n\n// 生命周期钩子\nonMounted(() => {\n\tconsole.log('案例展示页面已加载，开始获取后台数据');\n\t// 获取案例列表数据\n\t// fetchCaseList();\n});\n\n// 获取案例列表\nconst fetchCaseList = async () => {\n\tloading.value = true;\n\t\n\ttry {\n\t\t// 检查token是否存在（避免401未授权错误）\n\t\tconst hasToken = api.auth.hasToken();\n\t\tif (!hasToken) {\n\t\t\tconsole.warn('未找到token，可能需要重新登录');\n\t\t\t// 可以选择跳转到登录页面或使用游客模式\n\t\t}\n\t\t\n\t\t// 调用后台API获取案例数据\n\t\tconst response = await api.realCase.getList();\n\t\t\n\t\t// 处理API返回的数据格式，映射到前端需要的字段\n\t\tif (response && response.data) {\n\t\t\tcaseList.value = response.data.map(item => ({\n\t\t\t\tid: item.id || item.case_id,\n\t\t\t\ttitle: item.title || item.case_title,\n\t\t\t\ttype: item.type || item.case_type,\n\t\t\t\tdebtAmount: item.debt_amount || item.debtAmount,\n\t\t\t\tresolvedAmount: item.resolved_amount || item.resolvedAmount,\n\t\t\t\treductionRate: item.reduction_rate || item.reductionRate,\n\t\t\t\tdate: item.date || item.create_time,\n\t\t\t\tstatus: item.status || item.case_status,\n\t\t\t\tsummary: item.summary || item.description,\n\t\t\t\tcategory: item.category || mapTypeToCategory(item.type)\n\t\t\t}));\n\t\t} else {\n\t\t\t// 如果后台返回空数据，使用空数组\n\t\t\tcaseList.value = [];\n\t\t}\n\t\t\n\t\tconsole.log('案例数据加载成功:', caseList.value.length, '条记录');\n\t\t\n\t} catch (error) {\n\t\tconsole.error('获取案例列表失败:', error);\n\t\t\n\t\t// 显示用户友好的错误提示\n\t\tuni.showToast({\n\t\t\ttitle: '加载失败，请重试',\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t});\n\t\t\n\t\t// 设置空数组避免页面出错\n\t\tcaseList.value = [];\n\t} finally {\n\t\tloading.value = false;\n\t}\n};\n\n\n\n// 将案例类型映射到分类的辅助函数\nconst mapTypeToCategory = (type) => {\n\tif (!type) return 'other';\n\tconst typeStr = type.toString().toLowerCase();\n\tif (typeStr.includes('信用卡') || typeStr.includes('credit')) return 'credit_card';\n\tif (typeStr.includes('车贷') || typeStr.includes('car')) return 'car_loan';\n\tif (typeStr.includes('房贷') || typeStr.includes('mortgage')) return 'mortgage';\n\treturn 'other';\n};\n\n// 过滤后的案例列表\nconst filteredCases = computed(() => {\n\tlet filtered = caseList.value;\n\t\n\t// 按类型筛选\n\tif (activeFilter.value !== 'all') {\n\t\tfiltered = filtered.filter(item => item.category === activeFilter.value);\n\t}\n\t\n\t// 按搜索关键词筛选\n\tif (searchKeyword.value.trim()) {\n\t\tconst keyword = searchKeyword.value.trim().toLowerCase();\n\t\tfiltered = filtered.filter(item => \n\t\t\titem.title.toLowerCase().includes(keyword) ||\n\t\t\titem.type.toLowerCase().includes(keyword) ||\n\t\t\titem.summary.toLowerCase().includes(keyword)\n\t\t);\n\t}\n\t\n\treturn filtered;\n});\n\n// 处理搜索\nconst handleSearch = () => {\n\t// 触发计算属性重新计算\n\tconsole.log('搜索关键词：', searchKeyword.value);\n};\n\n// 处理筛选变化\nconst handleFilterChange = (filterValue) => {\n\tactiveFilter.value = filterValue;\n};\n// 跳转到云开发静态页面\nconst navigateTo = () => {\n\tapi.realCase.getDetails().then(res => {\n\t\tconsole.log('getDetails成功:', res);\n\t}).catch(err => {\n\t\tconsole.error('getDetails失败:', err);\n\t});\n\t/* uni.navigateTo({\n\t\turl: `/pages/webview/webview?url=${encodeURIComponent('https://www.eeclat.cn/jump_mp/#wechat_redirect')}`\n\t}); */\n\t/* wx.navigateToMiniProgram({\n\t\tappId: '',\n\t\tpath: 'https://prod-9gwr0pqvc081f7f4-1370735801.tcloudbaseapp.com/',\n\t\textraData: {\n\t\t\tfoo: 'bar'\n\t\t},\n\t\tenvVersion: 'develop',\n\t\tsuccess(res) {\n\t\t\t// 打开成功\n\t\t\tconsole.log(res,'打开静态开发页面成功');\n\t\t}\n\t}); */\n};\n// 跳转到详情页面\nconst navigateToDetail = async (caseId) => {\n\ttry {\n\t\t// 记录用户操作日志（根据系统要求记录按钮点击操作）\n\t\tawait recordUserOperation('案例展示', '查看详情', caseId);\n\t\t\n\t\t// 跳转到详情页面\n\t\tuni.navigateTo({\n\t\t\turl: `/pages/case_detail/case_detail?id=${caseId}`\n\t\t});\n\t} catch (error) {\n\t\tconsole.error('记录操作日志失败:', error);\n\t\t// 即使日志记录失败，仍然允许页面跳转\n\t\tuni.navigateTo({\n\t\t\turl: `/pages/case_detail/case_detail?id=${caseId}`\n\t\t});\n\t}\n};\n\n// 记录用户操作日志的函数\nconst recordUserOperation = async (menuName, buttonName, extraData = '') => {\n\ttry {\n\t\tconst pages = getCurrentPages();\n\t\tconst currentPage = pages[pages.length - 1];\n\t\tconst browserPath = currentPage.route;\n\t\t\n\t\t// 构造操作日志数据\n\t\tconst logData = {\n\t\t\tbutton_name: buttonName,       // 按钮名称\n\t\t\tbutton_type: buttonName,       // 按钮类型\n\t\t\tpage_url: `/${browserPath}`, // 浏览器路径\n\t\t\tpage_plate: menuName,           // 菜单名称\n\t\t\t// operation_time: new Date().toISOString(), // 操作时间\n\t\t\t// extra_data: extraData.toString() // 额外数据（如案例ID）\n\t\t};\n\t\t\n\t\t// 调用API记录操作日志\n\t\tawait api.operationLog.recordOperation(logData);\n\t\tconsole.log('用户操作日志记录成功:', logData);\n\t\t\n\t} catch (error) {\n\t\tconsole.error('记录用户操作日志失败:', error);\n\t\t// 不阻断正常业务流程\n\t}\n};\n\n// 格式化金额显示\nconst formatAmount = (amount) => {\n\tif (!amount) return '0.00';\n\treturn amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });\n};\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n\tswitch (status) {\n\t\tcase '调解成功':\n\t\t\treturn 'success';\n\t\tcase '延期还款':\n\t\t\treturn 'warning';\n\t\tcase '调解中':\n\t\t\treturn 'processing';\n\t\tdefault:\n\t\t\treturn 'default';\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.real-case-container {\n\t// min-height: 100vh;\n\t// background-color: #f5f5f5;\n\t// padding: 0 20rpx 30rpx 20rpx;\n\theight: calc(100% - 94px);\n    overflow-y: auto;\n    background-color: rgb(248, 250, 252);\n    padding: 30rpx 30rpx 140rpx;\n}\n\n.header{\n\tbackground-color: white;\n    box-shadow: rgba(0, 0, 0, 0.04) 0px 2px 8px;\n    margin-bottom: 30rpx;\n    border-radius: 24rpx;\n    padding: 46rpx;\n    transition: all 0.3s ease;\n    border-width: 2rpx;\n    border-style: solid;\n    border-color: rgba(0, 0, 0, 0.03);\n    border-image: initial;\n}\n.header-title{\n\tfont-size: 36rpx;\n    color: #333;\n    font-weight: 600;\n    margin-bottom: 20rpx;\n}\n.header-text{\n\tfont-size: 28rpx;\n    color: #666;\n    margin-bottom: 30rpx;\n}\n/* \n.search-filter-section {\n\tbackground-color: #ffffff;\n\tpadding: 30rpx 20rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.search-box {\n\tmargin-bottom: 30rpx;\n}\n\n.filter-tabs {\n\tdisplay: flex;\n\tgap: 20rpx;\n\toverflow-x: auto;\n}\n\n.filter-tab {\n\tpadding: 15rpx 30rpx;\n\tbackground-color: #f8f8f8;\n\tcolor: #666;\n\tborder-radius: 30rpx;\n\tfont-size: 26rpx;\n\twhite-space: nowrap;\n\ttransition: all 0.3s ease;\n}\n\n.filter-tab.active {\n\tbackground-color: #2979ff;\n\tcolor: #ffffff;\n} */\n\n.case-card {\n\tbackground-color: #ffffff;\n\tborder-radius: 12rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\tposition: relative;\n}\n\n.case-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-bottom: 20rpx;\n}\n\n.case-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tflex: 1;\n\tline-height: 1.4;\n}\n\n.case-date {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-left: 20rpx;\n}\n\n.case-tags {\n\tdisplay: flex;\n\tgap: 15rpx;\n\tmargin-bottom: 25rpx;\n}\n\n.case-tag {\n\tpadding: 8rpx 16rpx;\n\tfont-size: 22rpx;\n\tborder-radius: 20rpx;\n}\n\n.type-tag {\n\tbackground-color: #e3f2fd;\n\tcolor: #1976d2;\n}\n\n.status-tag {\n\t&.success {\n\t\tbackground-color: #e8f5e8;\n\t\tcolor: #4caf50;\n\t}\n\t\n\t&.warning {\n\t\tbackground-color: #fff3e0;\n\t\tcolor: #ff9800;\n\t}\n\t\n\t&.processing {\n\t\tbackground-color: #f3e5f5;\n\t\tcolor: #9c27b0;\n\t}\n\t\n\t&.default {\n\t\tbackground-color: #f5f5f5;\n\t\tcolor: #666;\n\t}\n}\n\n.amount-info {\n\tmargin-bottom: 25rpx;\n}\n\n.amount-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.amount-label {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.debt-amount {\n\tfont-size: 26rpx;\n\tcolor: #f44336;\n\tfont-weight: 600;\n}\n\n.resolved-amount {\n\tfont-size: 26rpx;\n\tcolor: #4caf50;\n\tfont-weight: 600;\n}\n\n.reduction-info {\n\ttext-align: right;\n\tmargin-top: 5rpx;\n}\n\n.reduction-rate {\n\tfont-size: 24rpx;\n\tcolor: #ff9800;\n\tfont-weight: 500;\n}\n\n.case-summary {\n\tmargin-bottom: 25rpx;\n}\n\n.summary-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n\toverflow: hidden;\n}\n\n.detail-button {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 15rpx 0;\n\tborder-top: 2rpx solid #f0f0f0;\n\tmargin-top: 20rpx;\n}\n\n.button-text {\n\tfont-size: 28rpx;\n\tcolor: #2979ff;\n\tmargin-right: 10rpx;\n}\n\n.arrow-icon {\n\tfont-size: 32rpx;\n\tcolor: #2979ff;\n\ttransform: rotate(90deg);\n}\n\n.empty-state, .loading-state {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 100rpx 0;\n}\n\n.empty-text, .loading-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* uni-easyinput 组件样式覆盖 */\n:deep(.uni-easyinput__content) {\n\theight: 80rpx;\n\tbackground-color: #f8f8f8;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tpadding: 0 30rpx;\n}\n\n:deep(.uni-easyinput__content-input) {\n\tfont-size: 28rpx;\n}\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/real_case/real_case.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "computed", "api"], "mappings": ";;;;;;AAsGA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AAYvBC,kBAAAA,UAAU,MAAM;AACfC,oBAAAA,MAAA,MAAA,OAAA,wCAAY,oBAAoB;AAAA,IAGjC,CAAC;AAoED,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;AACpC,UAAI,WAAW,SAAS;AAGxB,UAAI,aAAa,UAAU,OAAO;AACjC,mBAAW,SAAS,OAAO,UAAQ,KAAK,aAAa,aAAa,KAAK;AAAA,MACvE;AAGD,UAAI,cAAc,MAAM,QAAQ;AAC/B,cAAM,UAAU,cAAc,MAAM,KAAM,EAAC,YAAW;AACtD,mBAAW,SAAS;AAAA,UAAO,UAC1B,KAAK,MAAM,cAAc,SAAS,OAAO,KACzC,KAAK,KAAK,cAAc,SAAS,OAAO,KACxC,KAAK,QAAQ,cAAc,SAAS,OAAO;AAAA,QAC9C;AAAA,MACE;AAED,aAAO;AAAA,IACR,CAAC;AAaD,UAAM,aAAa,MAAM;AACxBC,gBAAAA,IAAI,SAAS,aAAa,KAAK,SAAO;AACrCF,iFAAY,iBAAiB,GAAG;AAAA,MAClC,CAAE,EAAE,MAAM,SAAO;AACfA,mFAAc,iBAAiB,GAAG;AAAA,MACpC,CAAE;AAAA,IAgBF;AAEA,UAAM,mBAAmB,OAAO,WAAW;AAC1C,UAAI;AAEH,cAAM,oBAAoB,QAAQ,QAAQ,MAAM;AAGhDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,qCAAqC,MAAM;AAAA,QACnD,CAAG;AAAA,MACD,SAAQ,OAAO;AACfA,mFAAc,aAAa,KAAK;AAEhCA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,qCAAqC,MAAM;AAAA,QACnD,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,sBAAsB,OAAO,UAAU,YAAY,YAAY,OAAO;AAC3E,UAAI;AACH,cAAM,QAAQ;AACd,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,cAAM,cAAc,YAAY;AAGhC,cAAM,UAAU;AAAA,UACf,aAAa;AAAA;AAAA,UACb,aAAa;AAAA;AAAA,UACb,UAAU,IAAI,WAAW;AAAA;AAAA,UACzB,YAAY;AAAA;AAAA;AAAA;AAAA,QAGf;AAGE,cAAME,cAAI,aAAa,gBAAgB,OAAO;AAC9CF,sBAAY,MAAA,MAAA,OAAA,wCAAA,eAAe,OAAO;AAAA,MAElC,SAAQ,OAAO;AACfA,mFAAc,eAAe,KAAK;AAAA,MAElC;AAAA,IACF;AAGA,UAAM,eAAe,CAAC,WAAW;AAChC,UAAI,CAAC;AAAQ,eAAO;AACpB,aAAO,OAAO,eAAe,SAAS,EAAE,uBAAuB,EAAC,CAAE;AAAA,IACnE;;;;;;;;;;;;;;;;;;;;;;;ACpSA,GAAG,WAAW,eAAe;"}