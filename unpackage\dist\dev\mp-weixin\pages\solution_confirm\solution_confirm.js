"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "solution_confirm",
  setup(__props) {
    const caseNumber = common_vendor.ref("");
    const workOrderData = common_vendor.ref({});
    const basicInfo = common_vendor.ref({
      // 基本信息
      case_number: "",
      initiate_date: "",
      case_status_cn: "",
      mediation_progress: ""
    });
    const workPlanData = common_vendor.ref([]);
    const selectedSolutionIndex = common_vendor.ref(0);
    const selectedPlanId = common_vendor.ref("");
    const solutions = common_vendor.ref([]);
    common_vendor.computed(() => {
      return solutions.value[selectedSolutionIndex.value];
    });
    const getSelectedPlan = () => {
      return workPlanData.value.find((plan) => plan.plan_id === selectedPlanId.value);
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:221", "方案确认页面加载，参数:", options);
      if (options && options.case_number) {
        handleUrlParams(options);
      }
    });
    const handleUrlParams = async (options) => {
      common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:231", "处理URL参数:", options);
      const { case_number, initiate_date, case_status_cn, mediation_progress } = options;
      const hasCompleteParams = case_number && initiate_date && case_status_cn && mediation_progress;
      if (hasCompleteParams) {
        common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:240", "检测到完整参数，直接显示基本信息");
        basicInfo.value.case_number = decodeURIComponent(case_number);
        basicInfo.value.initiate_date = decodeURIComponent(initiate_date);
        basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);
        basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);
        caseNumber.value = basicInfo.value.case_number;
        await fetchWorkPlanDetail(basicInfo.value.case_number);
      } else if (case_number) {
        common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:256", "仅有案件编号，调用接口获取详细信息");
        const decodedCaseNumber = decodeURIComponent(case_number);
        caseNumber.value = decodedCaseNumber;
        await fetchMediationSingleDetail(decodedCaseNumber);
        await fetchWorkPlanDetail(decodedCaseNumber);
      }
    };
    const fetchMediationSingleDetail = async (caseNumber2) => {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const result = await utils_api.api.mediationQuery.getSingleDetail(caseNumber2);
        common_vendor.index.hideLoading();
        if (result.state === "success" && result.data) {
          const data = result.data;
          basicInfo.value.case_number = data.case_number || caseNumber2;
          basicInfo.value.initiate_date = data.initiate_date || "";
          basicInfo.value.case_status_cn = data.case_status_cn || "";
          basicInfo.value.mediation_progress = data.mediation_progress || "";
        } else {
          common_vendor.index.showToast({
            title: result.msg || "获取案件信息失败",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:290", "获取案件详情失败:", error);
        common_vendor.index.showToast({
          title: "获取案件信息失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const fetchWorkPlanDetail = async (caseNumber2) => {
      const result = await utils_api.api.solution.getPlanDetail(caseNumber2);
      if (result.state === "success" && result.data) {
        workPlanData.value = result.data;
        common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:305", "方案详情获取成功:", result.data);
        if (result.data.length > 0) {
          selectedPlanId.value = result.data[0].plan_id;
          common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:310", "默认选择第一个方案:", result.data[0].plan_name);
        }
      } else {
        common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:313", "工作方案详情获取失败:", result.msg);
        workPlanData.value = [];
      }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:320", "使用默认方案数据");
    });
    const selectPlan = (planId) => {
      selectedPlanId.value = planId;
      common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:383", "当前选中的方案ID:", selectedPlanId.value);
    };
    const viewPlanDetail = (plan) => {
      const configDetails = plan.plan_config.map((config) => {
        let detail = `${config.title}: ${config.value}`;
        if (config.expression) {
          detail += `
计算公式: ${config.expression}`;
        }
        return detail;
      }).join("\n\n");
      common_vendor.index.showModal({
        title: `${plan.plan_name}详情`,
        content: configDetails,
        showCancel: false
      });
    };
    const handleConfirm = () => {
      if (workPlanData.value.length > 0) {
        if (!selectedPlanId.value) {
          common_vendor.index.showToast({
            title: "请选择一个方案",
            icon: "none"
          });
          return;
        }
        const selectedPlan = getSelectedPlan();
        const planName = (selectedPlan == null ? void 0 : selectedPlan.plan_name) || "未知方案";
        common_vendor.index.showModal({
          title: "确认方案",
          content: `您确定要选择"${planName}"吗？`,
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.showLoading({
                title: "处理中..."
              });
              common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:447", selectedPlan, "===selectedPlan");
              if (caseNumber.value) {
                utils_api.api.solution.confirmSolution(caseNumber.value, {
                  mediation_plan: selectedPlanId.value
                }).then((res2) => {
                  common_vendor.index.hideLoading();
                  if (res2.state === "success") {
                    common_vendor.index.showToast({
                      title: res2.msg || "方案确认成功",
                      icon: "success",
                      duration: 1500
                    });
                    setTimeout(() => {
                      common_vendor.index.navigateTo({
                        url: "/pages/agreement_signing/agreement_signing",
                        success: () => {
                          common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:467", "跳转到协议签署页面");
                        },
                        fail: (err) => {
                          common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:470", "跳转失败", err);
                          common_vendor.index.showToast({
                            title: "跳转失败",
                            icon: "none"
                          });
                        }
                      });
                    }, 2e3);
                  } else {
                    common_vendor.index.showToast({
                      title: res2.msg || "操作失败",
                      icon: "none"
                    });
                  }
                }).catch((err) => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:487", "确认方案失败", err);
                  common_vendor.index.showToast({
                    title: "确认方案失败",
                    icon: "none"
                  });
                });
              } else {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "缺少调解方案编号",
                  icon: "none"
                });
              }
            }
          }
        });
      } else {
        const selectedSolution = solutions.value[selectedSolutionIndex.value];
        if (!selectedSolution) {
          common_vendor.index.showToast({
            title: "请选择一个方案",
            icon: "none"
          });
          return;
        }
        common_vendor.index.showModal({
          title: "确认方案",
          content: `您确定要选择"${selectedSolution.title}"吗？`,
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.showLoading({
                title: "处理中..."
              });
              if (caseNumber.value) {
                utils_api.api.solution.confirmSolution(caseNumber.value, { solutionId: selectedSolution.id }).then((res2) => {
                  common_vendor.index.hideLoading();
                  if (res2.state === "success") {
                    common_vendor.index.showToast({
                      title: res2.msg || "方案确认成功",
                      icon: "success",
                      duration: 1500
                    });
                    setTimeout(() => {
                      common_vendor.index.navigateTo({
                        url: "/pages/agreement_signing/agreement_signing",
                        success: () => {
                          common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:543", "跳转到协议签署页面");
                        },
                        fail: (err) => {
                          common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:546", "跳转失败", err);
                          common_vendor.index.showToast({
                            title: "跳转失败",
                            icon: "none"
                          });
                        }
                      });
                    }, 2e3);
                  } else {
                    common_vendor.index.showToast({
                      title: res2.msg || "操作失败",
                      icon: "none"
                    });
                  }
                }).catch((err) => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:563", "确认方案失败", err);
                  common_vendor.index.showToast({
                    title: "确认方案失败",
                    icon: "none"
                  });
                });
              } else {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "缺少调解方案编号",
                  icon: "none"
                });
              }
            }
          }
        });
      }
    };
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.t(basicInfo.value.case_number),
        b: common_vendor.t(basicInfo.value.case_status_cn || workOrderData.value.case_status_cn),
        c: (basicInfo.value.case_status_cn || workOrderData.value.case_status_cn) === "进行中" ? 1 : "",
        d: common_vendor.t(basicInfo.value.initiate_date || workOrderData.value.createDate),
        e: workPlanData.value.length > 0
      }, workPlanData.value.length > 0 ? {
        f: common_vendor.f(workPlanData.value, (plan, planIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.t(plan.plan_name),
            b: selectedPlanId.value === plan.plan_id
          }, selectedPlanId.value === plan.plan_id ? {} : {}, {
            c: selectedPlanId.value === plan.plan_id ? 1 : "",
            d: common_vendor.f(plan.plan_config, (config, k1, i1) => {
              return {
                a: common_vendor.t(config.title),
                b: common_vendor.t(config.value),
                c: config.id
              };
            }),
            e: common_vendor.o(($event) => viewPlanDetail(plan), plan.plan_id),
            f: plan.plan_id,
            g: selectedPlanId.value === plan.plan_id ? 1 : "",
            h: common_vendor.o(($event) => selectPlan(plan.plan_id), plan.plan_id)
          });
        })
      } : {}, {
        g: workPlanData.value.length === 0
      }, workPlanData.value.length === 0 ? {} : {}, {
        h: workPlanData.value.length > 0 && selectedPlanId.value
      }, workPlanData.value.length > 0 && selectedPlanId.value ? {
        i: common_vendor.t(((_a = getSelectedPlan()) == null ? void 0 : _a.plan_name) || "方案")
      } : solutions.value.length > 0 ? {
        k: common_vendor.t(((_b = solutions.value[selectedSolutionIndex.value]) == null ? void 0 : _b.title) || "方案")
      } : {}, {
        j: solutions.value.length > 0,
        l: common_vendor.o(handleConfirm)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5489ef86"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/solution_confirm/solution_confirm.js.map
