<view class="protocol-preview-container data-v-3f6102ab"><view wx:if="{{a}}" class="long-image-preview data-v-3f6102ab"><view class="preview-header data-v-3f6102ab"><view class="header-right data-v-3f6102ab"><text class="progress-text data-v-3f6102ab">{{b}}%</text></view></view><scroll-view class="long-scroll-container data-v-3f6102ab" scroll-y="true" scroll-top="{{g}}" bindscroll="{{h}}" enhanced show-scrollbar="{{false}}"><view class="content-container data-v-3f6102ab"><view wx:if="{{c}}" class="loading-container data-v-3f6102ab"><view class="loading-icon data-v-3f6102ab"><view class="fas fa-spinner fa-spin data-v-3f6102ab"></view></view><text class="loading-text data-v-3f6102ab">正在加载协议内容...</text></view><view wx:else class="images-container data-v-3f6102ab"><view wx:for="{{d}}" wx:for-item="image" wx:key="e" class="svg-container data-v-3f6102ab"><image src="{{image.a}}" mode="widthFix" class="{{['protocol-page', 'svg-image', 'data-v-3f6102ab', image.b && 'first-page']}}" bindload="{{image.c}}" binderror="{{image.d}}" lazy-load/></view><image wx:for="{{e}}" wx:for-item="image" wx:key="a" src="{{image.b}}" mode="widthFix" class="{{['protocol-page', 'data-v-3f6102ab', image.c && 'first-page']}}" bindload="{{image.d}}" binderror="{{image.e}}" lazy-load/></view><view class="bottom-tip data-v-3f6102ab"><view class="tip-line data-v-3f6102ab"></view><text class="tip-text data-v-3f6102ab">协议内容已全部加载完成</text><view class="action-buttons data-v-3f6102ab"><button class="download-btn-small data-v-3f6102ab" bindtap="{{f}}"><view class="fas fa-download data-v-3f6102ab"></view> 下载协议 </button></view></view></view></scroll-view><view class="progress-indicator data-v-3f6102ab"><view class="progress-bar data-v-3f6102ab"><view class="progress-fill data-v-3f6102ab" style="{{'width:' + i}}"></view></view></view></view></view>