{"version": 3, "file": "api.js", "sources": ["utils/api.js"], "sourcesContent": ["// 统一API管理模块 - 集成定义和调用方法\n// 引入HTTP请求拦截器\nimport { request, authUtils, createRequest } from '@/server/require.js';\n// 引入用户状态管理\nimport userStore from '@/utils/user-store.js';\n\n// 创建不需要认证的请求函数（用于登录等接口）\nconst requestWithoutAuth = createRequest({ skipAuth: true });\n\n// ==================== API常量定义 ====================\n\n/**\n * 用户相关API\n */\nexport const USER_API = {\n  // 基础用户操作\n  INFO: '/user/user_info/',\n  LOGIN: '/user/login',\n  UPDATE: '/user/update',\n  BIND_PHONE: '/user/bind-phone',\n  SEND_CODE: '/user/send-code',\n  PHONE_LOGIN: '/user/phone-login',\n\n  // 用户操作日志\n  OPERATION_LOG: '/user/operation_log/'\n};\n\n/**\n * 微信认证相关API\n */\nexport const WECHAT_API = {\n  LOGIN: '/wechat/login/',\n  REFRESH: '/wechat/refresh/',\n  BIND: '/wechat/bind/',\n  UNBIND: '/wechat/unbind/',\n  USER_INFO: '/wechat/userinfo/',\n  FACEID_AUTH: '/wechat/faceid/auth/'  // 人脸核身认证接口\n};\n\n/**\n * 首页相关API\n */\nexport const HOME_API = {\n  GRID_DATA: '/home/<USER>'\n};\n\n/**\n * 调解查询相关API\n */\nexport const MEDIATION_QUERY_API = {\n  LIST: '/mediation_management/mediation_case/wechat/list',\n  DETAIL: '/mediation_management/mediation_case',\n  CASE_BY_NUMBER: '/mediation_management/mediation_case/',\n  CASE_COUNT_BY_IDENTITY: '/mediation_management/mediation_case/wechat/by_debtor/'\n};\n\n/**\n * 相关API\n */\nexport const WORK_ORDER_API = {\n  DETAIL: '/mediation_management/mediation_case',\n  ACCEPT: '/mediation_management/mediation_case/wechat',\n  REJECT: '/work-order/reject'\n};\n\n/**\n * 调解方案相关API\n */\nexport const SOLUTION_API = {\n  DETAIL: '/solution/detail',\n  CONFIRM: '/mediation_management/mediation_case/wechat',\n  ADJUST: '/solution/adjust'\n};\n\n/**\n * 债权确认相关API\n */\nexport const DEBT_CONFIRM_API = {\n  LIST: '/debt-confirm/list',\n  DETAIL: '/debt-confirm/detail',\n  SUBMIT: '/debt-confirm/submit'\n};\n\n/**\n * 调解投诉相关API\n */\nexport const MEDIATION_COMPLAINT_API = {\n  LIST: '/mediation-complaint/list',\n  DETAIL: '/mediation-complaint/detail',\n  SUBMIT: '/mediation-complaint/submit'\n};\n\n/**\n * 案例展示相关API\n */\nexport const REAL_CASE_API = {\n  LIST: '/case_display/case_display/',\n  DETAIL: '/real-case/detail',\n  SUBMIT: '/user/files/download/8de29944-dd25-40ad-a5b9-c581ba9d3f5e/',\n};\n\n/**\n * 意见反馈相关API\n */\nexport const FEEDBACK_API = {\n  SUBMIT: '/feedback/submit',\n  HISTORY: '/feedback/history'\n};\n\n/**\n * 所有API的统一导出\n */\nexport const API_PATHS = {\n  USER: USER_API,\n  WECHAT: WECHAT_API,\n  HOME: HOME_API,\n  MEDIATION_QUERY: MEDIATION_QUERY_API,\n  WORK_ORDER: WORK_ORDER_API,\n  SOLUTION: SOLUTION_API,\n  DEBT_CONFIRM: DEBT_CONFIRM_API,\n  MEDIATION_COMPLAINT: MEDIATION_COMPLAINT_API,\n  REAL_CASE: REAL_CASE_API,\n  FEEDBACK: FEEDBACK_API\n};\n\n/**\n * 根据模板和参数生成完整URL\n * @param {string} pathTemplate - 模板（如 '/user/detail/{id}'）\n * @param {object} params - 参数对象\n * @returns {string} 完整的URL\n */\nexport const buildApiPath = (pathTemplate, params = {}) => {\n  let path = pathTemplate;\n\n  // 替换中的参数占位符\n  Object.keys(params).forEach(key => {\n    path = path.replace(`{${key}}`, params[key]);\n  });\n\n  return path;\n};\n\n/**\n * 获取带参数的API辅助函数\n */\nexport const getApiPath = {\n  // 获取用户详情\n  userDetail: (id) => `${USER_API.DETAIL}/${id}`,\n\n  // 获取调解查询详情==》调解确认（调解信息、相关文件）\n  mediationQueryDetail: (id) => `${MEDIATION_QUERY_API.DETAIL}/${id}/content/`,\n\n  // 获取调解查询详情==》（获取单条调解数据基本信息：案件号、状态、日期）\n  mediationSingleDetail: (id) => `${MEDIATION_QUERY_API.LIST}/${id}`,\n\n  // 获取待确认案件信息详情\n  workOrderDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/content/`,\n\n  // 获取进行中方案信息详情\n  workPlanDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/plan_config/`,\n\n  // 获取调解查询详情==》调解确认（接受调解）\n  workOrderAccept: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/confirm_status/`,\n\n  // 获取调解查询详情==》已完成\n  workOrderCompleted: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/detail/`,\n\n  // 获取拒绝\n  workOrderReject: (id) => `${WORK_ORDER_API.REJECT}/${id}`,\n\n  // 获取调解方案详情\n  solutionDetail: (orderId) => `${SOLUTION_API.DETAIL}/${orderId}`,\n\n  // 获取调解方案确认\n  solutionConfirm: (caseNumber) => `${SOLUTION_API.CONFIRM}/${caseNumber}/update_mediation_plan/`,\n\n  // 获取调解方案调整\n  solutionAdjust: (orderId) => `${SOLUTION_API.ADJUST}/${orderId}`,\n\n  // 获取债权确认详情\n  debtConfirmDetail: (id) => `${DEBT_CONFIRM_API.DETAIL}/${id}`,\n\n  // 获取调解投诉详情\n  mediationComplaintDetail: (id) => `${MEDIATION_COMPLAINT_API.DETAIL}/${id}`,\n\n  // 获取案例详情\n  realCaseDetail: (id) => `${REAL_CASE_API.DETAIL}/${id}`,\n\n  realCase: () => `${REAL_CASE_API.SUBMIT}`\n};\n\n// ==================== API调用方法 ====================\n\n// API调用模块 - 保持原有结构，确保向后兼容\nexport const api = {\n  // Token管理工具 - 使用server/require.js中的authUtils\n  auth: authUtils,\n  \n  // 用户相关\n  user: {\n    // 获取用户信息\n    getUserInfo: () => {\n      return request({\n        url: USER_API.INFO,\n        method: 'GET'\n      });\n    },\n\n    // 普通登录 - 增强版本，支持登录成功后自动存储认证信息\n    login: async (data) => {\n      try {\n        // 发送登录请求（跳过Authorization头部）\n        const response = await requestWithoutAuth({\n          url: USER_API.LOGIN,\n          method: 'POST',\n          data\n        });\n\n        // 检查登录是否成功\n        if (response && response.success !== false) {\n          // 获取登录响应数据\n          const loginData = response.data || response;\n          \n          // 1. 存储token信息（支持新旧格式）\n          if (loginData.access_token && loginData.token_type) {\n            // 新格式：使用access_token和token_type\n            authUtils.setTokenInfo({\n              access_token: loginData.access_token,\n              token_type: loginData.token_type,\n              expires_in: loginData.expires_in\n            });\n          } else if (loginData.token) {\n            // 旧格式兼容：直接设置token\n            authUtils.setToken(loginData.token);\n          }\n\n          // 2. 存储refresh_token\n          if (loginData.refresh_token) {\n            authUtils.setRefreshToken(loginData.refresh_token);\n          }\n\n          // 3. 存储openid\n          if (loginData.openid) {\n            uni.setStorageSync('openid', loginData.openid);\n          }\n\n          // 4. 存储unionid（如果有）\n          if (loginData.unionid) {\n            uni.setStorageSync('unionid', loginData.unionid);\n          }\n\n          // 5. 存储用户信息\n          if (loginData.user_info || loginData.userInfo) {\n            const userInfo = loginData.user_info || loginData.userInfo;\n            userStore.setUserInfo(userInfo);\n          }\n\n          // 6. 存储微信用户信息（如果有）\n          if (loginData.wechat_userInfo || loginData.wechatInfo) {\n            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;\n            userStore.setWechatUserInfo(wechatInfo);\n          }\n\n          console.log('登录成功，认证信息已自动存储');\n        }\n\n        return response;\n      } catch (error) {\n        console.error('登录失败:', error);\n        throw error;\n      }\n    },\n\n    // 更新用户信息\n    updateUserInfo: (data) => {\n      return request({\n        url: USER_API.UPDATE,\n        method: 'PUT',\n        data\n      });\n    },\n\n    // 绑定手机号\n    bindPhone: (data) => {\n      return request({\n        url: USER_API.BIND_PHONE,\n        method: 'POST',\n        data\n      });\n    },\n\n    // 发送验证码（跳过Authorization头部）\n    sendVerifyCode: (data) => {\n      return requestWithoutAuth({\n        url: USER_API.SEND_CODE,\n        method: 'POST',\n        data\n      });\n    },\n\n    // 手机号登录 - 增强版本，支持登录成功后自动存储认证信息\n    phoneLogin: async (data) => {\n      try {\n        // 发送手机号登录请求（跳过Authorization头部）\n        const response = await requestWithoutAuth({\n          url: USER_API.PHONE_LOGIN,\n          method: 'POST',\n          data\n        });\n\n        // 检查登录是否成功\n        if (response && response.success !== false) {\n          // 获取登录响应数据\n          const loginData = response.data || response;\n          \n          // 1. 存储token信息\n          if (loginData.access_token && loginData.token_type) {\n            authUtils.setTokenInfo({\n              access_token: loginData.access_token,\n              token_type: loginData.token_type,\n              expires_in: loginData.expires_in\n            });\n          } else if (loginData.token) {\n            // 兼容旧格式\n            authUtils.setToken(loginData.token);\n          }\n\n          // 2. 存储refresh_token\n          if (loginData.refresh_token) {\n            authUtils.setRefreshToken(loginData.refresh_token);\n          }\n\n          // 3. 存储openid（如果有）\n          if (loginData.openid) {\n            uni.setStorageSync('openid', loginData.openid);\n          }\n\n          // 4. 存储用户信息\n          if (loginData.user_info || loginData.userInfo) {\n            const userInfo = loginData.user_info || loginData.userInfo;\n            userStore.setUserInfo(userInfo);\n          }\n\n          console.log('手机号登录成功，认证信息已自动存储');\n        }\n\n        return response;\n      } catch (error) {\n        console.error('手机号登录失败:', error);\n        throw error;\n      }\n    }\n  },\n\n  // 微信认证相关\n  wechat: {\n    // 微信登录 - 增强版本，支持登录成功后自动存储认证信息\n    login: async (data) => {\n      try {\n        // 发送微信登录请求（跳过Authorization头部）\n        const response = await requestWithoutAuth({\n          url: WECHAT_API.LOGIN,\n          method: 'POST',\n          data\n        });\n\n        // 检查登录是否成功\n        if (response && response.success !== false) {\n          // 获取登录响应数据\n          const loginData = response.data || response;\n          \n          // 1. 存储token信息\n          if (loginData.access_token && loginData.token_type) {\n            authUtils.setTokenInfo({\n              access_token: loginData.access_token,\n              token_type: loginData.token_type,\n              expires_in: loginData.expires_in\n            });\n          } else if (loginData.token) {\n            // 兼容旧格式\n            authUtils.setToken(loginData.token);\n          }\n\n          // 2. 存储refresh_token\n          if (loginData.refresh_token) {\n            authUtils.setRefreshToken(loginData.refresh_token);\n          }\n\n          // 3. 存储openid\n          if (loginData.openid) {\n            uni.setStorageSync('openid', loginData.openid);\n          }\n\n          // 4. 存储unionid（如果有）\n          if (loginData.unionid) {\n            uni.setStorageSync('unionid', loginData.unionid);\n          }\n\n          // 5. 存储sessionKey（如果有）\n          if (loginData.sessionKey || loginData.session_key) {\n            uni.setStorageSync('sessionKey', loginData.sessionKey || loginData.session_key);\n          }\n\n          // 6. 存储用户信息\n          if (loginData.user_info || loginData.userInfo) {\n            const userInfo = loginData.user_info || loginData.userInfo;\n            userStore.setUserInfo(userInfo);\n          }\n\n          // 7. 存储微信用户信息\n          if (loginData.wechat_userInfo || loginData.wechatInfo) {\n            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;\n            userStore.setWechatUserInfo(wechatInfo);\n          }\n\n          console.log('微信登录成功，认证信息已自动存储');\n        }\n\n        return response;\n      } catch (error) {\n        console.error('微信登录失败:', error);\n        throw error;\n      }\n    },\n\n    // 微信token刷新（使用refresh_token，跳过Authorization头部）\n    refresh: (data) => {\n      return requestWithoutAuth({\n        url: WECHAT_API.REFRESH,\n        method: 'POST',\n        data\n      });\n    },\n\n    // 绑定微信\n    bind: (data) => {\n      return request({\n        url: WECHAT_API.BIND,\n        method: 'POST',\n        data\n      });\n    },\n\n    // 解绑微信\n    unbind: () => {\n      return request({\n        url: WECHAT_API.UNBIND,\n        method: 'POST'\n      });\n    },\n\n    // 获取微信用户信息\n    getUserInfo: (data) => {\n      return request({\n        url: WECHAT_API.USER_INFO,\n        method: 'POST',\n        data\n      });\n    },\n\n    // 人脸核身认证\n    faceIdAuth: (data) => {\n      return request({\n        url: WECHAT_API.FACEID_AUTH,\n        method: 'POST',\n        data\n      });\n    }\n  },\n  \n  // 首页相关\n  home: {\n    // 获取首页卡片数据（公开数据，跳过Authorization头部）\n    getGridData: () => {\n      return requestWithoutAuth({\n        url: HOME_API.GRID_DATA,\n        method: 'GET'\n      });\n    }\n  },\n  \n  // 调解查询\n  mediationQuery: {\n    // 获取调解查询列表\n    getAuthenticatedList: (params) => {\n      return request({\n        url: MEDIATION_QUERY_API.LIST,\n        method: 'GET',\n        data: params\n      });\n    },\n\n    // 获取调解查询详情\n    getDetail: (id) => {\n      return request({\n        url: getApiPath.mediationQueryDetail(id),\n        method: 'GET'\n      });\n    },\n\n    // 获取单条调解数据基本信息（案件号、状态、日期）\n    getSingleDetail: (id) => {\n      return request({\n        url: getApiPath.mediationSingleDetail(id),\n        method: 'GET'\n      });\n    },\n\n    // 根据调解案件号查询调解案件\n    getCaseByNumber: (caseNumber) => {\n      return request({\n        url: MEDIATION_QUERY_API.CASE_BY_NUMBER,\n        method: 'GET',\n        data: {\n          case_number: caseNumber\n        }\n      });\n    },\n\n    // 根据身份信息查询调解案件数量\n    getCaseCountByIdentity: (params) => {\n      return request({\n        url: MEDIATION_QUERY_API.CASE_COUNT_BY_IDENTITY,\n        method: 'GET',\n        data: params\n      });\n    }\n  },\n  \n  // 调解信息相关\n  workOrder: {\n    // 获取详情\n    getDetail: (id) => {\n      return request({\n        url: getApiPath.workOrderDetail(id),\n        method: 'GET'\n      });\n    },\n\n    // 接受调解\n    acceptWorkOrder: (id) => {\n      return request({\n        url: getApiPath.workOrderAccept(id),\n        method: 'PUT'\n      });\n    },\n\n    // 获取已完成的调解信息，还款方案\n    getCompletedWorkOrder: (id) => {\n      return request({\n        url: getApiPath.workOrderCompleted(id),\n        method: 'GET'\n      });\n    },\n    // 拒绝\n    /* rejectWorkOrder: (id, reason) => {\n      return request({\n        url: getApiPath.workOrderReject(id),\n        method: 'POST',\n        data: { reason }\n      });\n    } */\n  },\n  \n  // 调解方案相关\n  solution: {\n    // 获取调解方案详情\n    getDetail: (orderId) => {\n      return request({\n        url: getApiPath.solutionDetail(orderId),\n        method: 'GET'\n      });\n    },\n\n    // 获取工作方案详情配置数据\n    getPlanDetail: (id) => {\n      return request({\n        url: getApiPath.workPlanDetail(id),\n        method: 'GET'\n      });\n    },\n\n    // 确认调解方案\n    confirmSolution: (caseNumber, data) => {\n      return request({\n        url: getApiPath.solutionConfirm(caseNumber),\n        method: 'PUT',\n        data\n      });\n    },\n    \n    // 申请调整方案\n    adjustSolution: (orderId, data) => {\n      return request({\n        url: getApiPath.solutionAdjust(orderId),\n        method: 'POST',\n        data\n      });\n    }\n  },\n  \n  // 债权确认\n  debtConfirm: {\n    // 获取债权确认列表\n    getList: (params) => {\n      return request({\n        url: DEBT_CONFIRM_API.LIST,\n        method: 'GET',\n        data: params\n      });\n    },\n    \n    // 获取债权确认详情\n    getDetail: (id) => {\n      return request({\n        url: getApiPath.debtConfirmDetail(id),\n        method: 'GET'\n      });\n    },\n    \n    // 提交债权确认\n    submit: (data) => {\n      return request({\n        url: DEBT_CONFIRM_API.SUBMIT,\n        method: 'POST',\n        data\n      });\n    }\n  },\n  \n  // 调解投诉\n  mediationComplaint: {\n    // 获取投诉列表\n    getList: (params) => {\n      return request({\n        url: MEDIATION_COMPLAINT_API.LIST,\n        method: 'GET',\n        data: params\n      });\n    },\n    \n    // 获取投诉详情\n    getDetail: (id) => {\n      return request({\n        url: getApiPath.mediationComplaintDetail(id),\n        method: 'GET'\n      });\n    },\n    \n    // 提交投诉\n    submit: (data) => {\n      return request({\n        url: MEDIATION_COMPLAINT_API.SUBMIT,\n        method: 'POST',\n        data\n      });\n    }\n  },\n  \n  // 案例展示\n  realCase: {\n    // 获取案例列表（公开数据，跳过Authorization头部）\n    getList: (params) => {\n      return requestWithoutAuth({\n        url: REAL_CASE_API.LIST,\n        method: 'GET',\n        data: params\n      });\n    },\n    \n    // 获取案例详情（公开数据，跳过Authorization头部）\n    getDetail: (id) => {\n      return requestWithoutAuth({\n        url: getApiPath.realCaseDetail(id),\n        method: 'GET'\n      });\n    },\n\n    getDetails: () => {\n      return request({\n        url: getApiPath.realCase(),\n        method: 'GET'\n      });\n    }\n     /* getSingleDetail: (id) => {\n      return request({\n        url: getApiPath.mediationSingleDetail(id),\n        method: 'GET'\n      });\n    }, */\n  },\n\n  // 用户操作日志 - 支持操作记录功能\n  operationLog: {\n    // 记录用户操作\n    recordOperation: (data) => {\n      return request({\n        url: USER_API.OPERATION_LOG,\n        method: 'POST',\n        data\n      }).catch(error => {\n        // 操作日志记录失败时静默处理，不影响主流程\n        console.warn('操作日志记录失败:', error.message);\n        return Promise.resolve();\n      });\n    }\n  },\n  \n  // 意见反馈\n  feedback: {\n    // 提交反馈\n    submitFeedback: (data) => {\n      return request({\n        url: FEEDBACK_API.SUBMIT,\n        method: 'POST',\n        data\n      });\n    },\n    \n    // 获取反馈历史\n    getFeedbackHistory: () => {\n      return request({\n        url: FEEDBACK_API.HISTORY,\n        method: 'GET'\n      });\n    }\n  }\n};\n\nexport default api; "], "names": ["createRequest", "authUtils", "request", "uni", "userStore"], "mappings": ";;;;AAOA,MAAM,qBAAqBA,eAAAA,cAAc,EAAE,UAAU,KAAM,CAAA;AAOpD,MAAM,WAAW;AAAA;AAAA,EAEtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA;AAAA,EAGb,eAAe;AACjB;AAKO,MAAM,aAAa;AAAA,EACxB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA;AACf;AAKO,MAAM,WAAW;AAAA,EACtB,WAAW;AACb;AAKO,MAAM,sBAAsB;AAAA,EACjC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,wBAAwB;AAC1B;AAKO,MAAM,iBAAiB;AAAA,EAC5B,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAKO,MAAM,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AACV;AAKO,MAAM,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAKO,MAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAKO,MAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAKO,MAAM,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AACX;AAsCO,MAAM,aAAa;AAAA;AAAA,EAExB,YAAY,CAAC,OAAO,GAAG,SAAS,MAAM,IAAI,EAAE;AAAA;AAAA,EAG5C,sBAAsB,CAAC,OAAO,GAAG,oBAAoB,MAAM,IAAI,EAAE;AAAA;AAAA,EAGjE,uBAAuB,CAAC,OAAO,GAAG,oBAAoB,IAAI,IAAI,EAAE;AAAA;AAAA,EAGhE,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,gBAAgB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGtD,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,oBAAoB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAG1D,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,gBAAgB,CAAC,YAAY,GAAG,aAAa,MAAM,IAAI,OAAO;AAAA;AAAA,EAG9D,iBAAiB,CAAC,eAAe,GAAG,aAAa,OAAO,IAAI,UAAU;AAAA;AAAA,EAGtE,gBAAgB,CAAC,YAAY,GAAG,aAAa,MAAM,IAAI,OAAO;AAAA;AAAA,EAG9D,mBAAmB,CAAC,OAAO,GAAG,iBAAiB,MAAM,IAAI,EAAE;AAAA;AAAA,EAG3D,0BAA0B,CAAC,OAAO,GAAG,wBAAwB,MAAM,IAAI,EAAE;AAAA;AAAA,EAGzE,gBAAgB,CAAC,OAAO,GAAG,cAAc,MAAM,IAAI,EAAE;AAAA,EAErD,UAAU,MAAM,GAAG,cAAc,MAAM;AACzC;AAKY,MAAC,MAAM;AAAA;AAAA,EAEjB,MAAMC,eAAS;AAAA;AAAA,EAGf,MAAM;AAAA;AAAA,IAEJ,aAAa,MAAM;AACjB,aAAOC,uBAAQ;AAAA,QACb,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,OAAO,OAAO,SAAS;AACrB,UAAI;AAEF,cAAM,WAAW,MAAM,mBAAmB;AAAA,UACxC,KAAK,SAAS;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,QACV,CAAS;AAGD,YAAI,YAAY,SAAS,YAAY,OAAO;AAE1C,gBAAM,YAAY,SAAS,QAAQ;AAGnC,cAAI,UAAU,gBAAgB,UAAU,YAAY;AAElDD,2BAAAA,UAAU,aAAa;AAAA,cACrB,cAAc,UAAU;AAAA,cACxB,YAAY,UAAU;AAAA,cACtB,YAAY,UAAU;AAAA,YACpC,CAAa;AAAA,UACb,WAAqB,UAAU,OAAO;AAE1BA,2BAAAA,UAAU,SAAS,UAAU,KAAK;AAAA,UACnC;AAGD,cAAI,UAAU,eAAe;AAC3BA,2BAAAA,UAAU,gBAAgB,UAAU,aAAa;AAAA,UAClD;AAGD,cAAI,UAAU,QAAQ;AACpBE,0BAAAA,MAAI,eAAe,UAAU,UAAU,MAAM;AAAA,UAC9C;AAGD,cAAI,UAAU,SAAS;AACrBA,0BAAAA,MAAI,eAAe,WAAW,UAAU,OAAO;AAAA,UAChD;AAGD,cAAI,UAAU,aAAa,UAAU,UAAU;AAC7C,kBAAM,WAAW,UAAU,aAAa,UAAU;AAClDC,sCAAU,YAAY,QAAQ;AAAA,UAC/B;AAGD,cAAI,UAAU,mBAAmB,UAAU,YAAY;AACrD,kBAAM,aAAa,UAAU,mBAAmB,UAAU;AAC1DA,sCAAU,kBAAkB,UAAU;AAAA,UACvC;AAEDD,wBAAAA,MAAY,MAAA,OAAA,uBAAA,gBAAgB;AAAA,QAC7B;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,uBAAA,SAAS,KAAK;AAC5B,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,CAAC,SAAS;AACxB,aAAOD,uBAAQ;AAAA,QACb,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,CAAC,SAAS;AACnB,aAAOA,uBAAQ;AAAA,QACb,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,CAAC,SAAS;AACxB,aAAO,mBAAmB;AAAA,QACxB,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO,SAAS;AAC1B,UAAI;AAEF,cAAM,WAAW,MAAM,mBAAmB;AAAA,UACxC,KAAK,SAAS;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,QACV,CAAS;AAGD,YAAI,YAAY,SAAS,YAAY,OAAO;AAE1C,gBAAM,YAAY,SAAS,QAAQ;AAGnC,cAAI,UAAU,gBAAgB,UAAU,YAAY;AAClDD,2BAAAA,UAAU,aAAa;AAAA,cACrB,cAAc,UAAU;AAAA,cACxB,YAAY,UAAU;AAAA,cACtB,YAAY,UAAU;AAAA,YACpC,CAAa;AAAA,UACb,WAAqB,UAAU,OAAO;AAE1BA,2BAAAA,UAAU,SAAS,UAAU,KAAK;AAAA,UACnC;AAGD,cAAI,UAAU,eAAe;AAC3BA,2BAAAA,UAAU,gBAAgB,UAAU,aAAa;AAAA,UAClD;AAGD,cAAI,UAAU,QAAQ;AACpBE,0BAAAA,MAAI,eAAe,UAAU,UAAU,MAAM;AAAA,UAC9C;AAGD,cAAI,UAAU,aAAa,UAAU,UAAU;AAC7C,kBAAM,WAAW,UAAU,aAAa,UAAU;AAClDC,sCAAU,YAAY,QAAQ;AAAA,UAC/B;AAEDD,wBAAAA,MAAA,MAAA,OAAA,uBAAY,mBAAmB;AAAA,QAChC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,uBAAA,YAAY,KAAK;AAC/B,cAAM;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,QAAQ;AAAA;AAAA,IAEN,OAAO,OAAO,SAAS;AACrB,UAAI;AAEF,cAAM,WAAW,MAAM,mBAAmB;AAAA,UACxC,KAAK,WAAW;AAAA,UAChB,QAAQ;AAAA,UACR;AAAA,QACV,CAAS;AAGD,YAAI,YAAY,SAAS,YAAY,OAAO;AAE1C,gBAAM,YAAY,SAAS,QAAQ;AAGnC,cAAI,UAAU,gBAAgB,UAAU,YAAY;AAClDF,2BAAAA,UAAU,aAAa;AAAA,cACrB,cAAc,UAAU;AAAA,cACxB,YAAY,UAAU;AAAA,cACtB,YAAY,UAAU;AAAA,YACpC,CAAa;AAAA,UACb,WAAqB,UAAU,OAAO;AAE1BA,2BAAAA,UAAU,SAAS,UAAU,KAAK;AAAA,UACnC;AAGD,cAAI,UAAU,eAAe;AAC3BA,2BAAAA,UAAU,gBAAgB,UAAU,aAAa;AAAA,UAClD;AAGD,cAAI,UAAU,QAAQ;AACpBE,0BAAAA,MAAI,eAAe,UAAU,UAAU,MAAM;AAAA,UAC9C;AAGD,cAAI,UAAU,SAAS;AACrBA,0BAAAA,MAAI,eAAe,WAAW,UAAU,OAAO;AAAA,UAChD;AAGD,cAAI,UAAU,cAAc,UAAU,aAAa;AACjDA,0BAAG,MAAC,eAAe,cAAc,UAAU,cAAc,UAAU,WAAW;AAAA,UAC/E;AAGD,cAAI,UAAU,aAAa,UAAU,UAAU;AAC7C,kBAAM,WAAW,UAAU,aAAa,UAAU;AAClDC,sCAAU,YAAY,QAAQ;AAAA,UAC/B;AAGD,cAAI,UAAU,mBAAmB,UAAU,YAAY;AACrD,kBAAM,aAAa,UAAU,mBAAmB,UAAU;AAC1DA,sCAAU,kBAAkB,UAAU;AAAA,UACvC;AAEDD,wBAAAA,MAAA,MAAA,OAAA,uBAAY,kBAAkB;AAAA,QAC/B;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,uBAAc,WAAW,KAAK;AAC9B,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,CAAC,SAAS;AACjB,aAAO,mBAAmB;AAAA,QACxB,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,CAAC,SAAS;AACd,aAAOD,uBAAQ;AAAA,QACb,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ,MAAM;AACZ,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,CAAC,SAAS;AACrB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,CAAC,SAAS;AACpB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,MAAM;AAAA;AAAA,IAEJ,aAAa,MAAM;AACjB,aAAO,mBAAmB;AAAA,QACxB,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB;AAAA;AAAA,IAEd,sBAAsB,CAAC,WAAW;AAChC,aAAOA,uBAAQ;AAAA,QACb,KAAK,oBAAoB;AAAA,QACzB,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,CAAC,OAAO;AACjB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,qBAAqB,EAAE;AAAA,QACvC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,CAAC,OAAO;AACvB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,sBAAsB,EAAE;AAAA,QACxC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,CAAC,eAAe;AAC/B,aAAOA,uBAAQ;AAAA,QACb,KAAK,oBAAoB;AAAA,QACzB,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,aAAa;AAAA,QACd;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB,CAAC,WAAW;AAClC,aAAOA,uBAAQ;AAAA,QACb,KAAK,oBAAoB;AAAA,QACzB,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,WAAW;AAAA;AAAA,IAET,WAAW,CAAC,OAAO;AACjB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,gBAAgB,EAAE;AAAA,QAClC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,CAAC,OAAO;AACvB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,gBAAgB,EAAE;AAAA,QAClC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB,CAAC,OAAO;AAC7B,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,mBAAmB,EAAE;AAAA,QACrC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF;AAAA;AAAA,EAGD,UAAU;AAAA;AAAA,IAER,WAAW,CAAC,YAAY;AACtB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,eAAe,OAAO;AAAA,QACtC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,CAAC,OAAO;AACrB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,eAAe,EAAE;AAAA,QACjC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,CAAC,YAAY,SAAS;AACrC,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,gBAAgB,UAAU;AAAA,QAC1C,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,CAAC,SAAS,SAAS;AACjC,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,eAAe,OAAO;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,aAAa;AAAA;AAAA,IAEX,SAAS,CAAC,WAAW;AACnB,aAAOA,uBAAQ;AAAA,QACb,KAAK,iBAAiB;AAAA,QACtB,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,CAAC,OAAO;AACjB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,kBAAkB,EAAE;AAAA,QACpC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ,CAAC,SAAS;AAChB,aAAOA,uBAAQ;AAAA,QACb,KAAK,iBAAiB;AAAA,QACtB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB;AAAA;AAAA,IAElB,SAAS,CAAC,WAAW;AACnB,aAAOA,uBAAQ;AAAA,QACb,KAAK,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,CAAC,OAAO;AACjB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,yBAAyB,EAAE;AAAA,QAC3C,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ,CAAC,SAAS;AAChB,aAAOA,uBAAQ;AAAA,QACb,KAAK,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,UAAU;AAAA;AAAA,IAER,SAAS,CAAC,WAAW;AACnB,aAAO,mBAAmB;AAAA,QACxB,KAAK,cAAc;AAAA,QACnB,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,CAAC,OAAO;AACjB,aAAO,mBAAmB;AAAA,QACxB,KAAK,WAAW,eAAe,EAAE;AAAA,QACjC,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA,IAED,YAAY,MAAM;AAChB,aAAOA,uBAAQ;AAAA,QACb,KAAK,WAAW,SAAU;AAAA,QAC1B,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF;AAAA;AAAA,EAGD,cAAc;AAAA;AAAA,IAEZ,iBAAiB,CAAC,SAAS;AACzB,aAAOA,uBAAQ;AAAA,QACb,KAAK,SAAS;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,MACR,CAAO,EAAE,MAAM,WAAS;AAEhBC,sBAAa,MAAA,MAAA,QAAA,uBAAA,aAAa,MAAM,OAAO;AACvC,eAAO,QAAQ;MACvB,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,UAAU;AAAA;AAAA,IAER,gBAAgB,CAAC,SAAS;AACxB,aAAOD,uBAAQ;AAAA,QACb,KAAK,aAAa;AAAA,QAClB,QAAQ;AAAA,QACR;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,MAAM;AACxB,aAAOA,uBAAQ;AAAA,QACb,KAAK,aAAa;AAAA,QAClB,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AAAA,EACF;AACH;;"}