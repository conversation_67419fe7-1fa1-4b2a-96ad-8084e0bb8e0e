{"version": 3, "file": "protocol_preview.js", "sources": ["pages/protocol_preview/protocol_preview.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvdG9jb2xfcHJldmlldy9wcm90b2NvbF9wcmV2aWV3LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"protocol-preview-container\">\r\n\t\t<!-- 预览选项 -->\r\n\t\t<!-- <view class=\"preview-options\">\r\n\t\t\t<button class=\"option-btn image-preview\" @click=\"viewAsImages\">\r\n\t\t\t\t<i class=\"fas fa-file-text\"></i>\r\n\t\t\t\t<text>在线预览</text>\r\n\t\t\t\t<text class=\"option-desc\">长图连续阅读</text>\r\n\t\t\t</button>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- 长图预览模式 -->\r\n\t\t<view v-if=\"showImagePreview\" class=\"long-image-preview\">\r\n\t\t\t<view class=\"preview-header\">\r\n\t\t\t\t<!-- <view class=\"header-left\">\r\n\t\t\t\t\t<button class=\"back-btn\" @click=\"closeImagePreview\">\r\n\t\t\t\t\t\t<i class=\"fas fa-arrow-left\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<text class=\"doc-title\">调解协议书</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"header-right\">\r\n\t\t\t\t\t<text class=\"progress-text\">{{Math.round(scrollProgress)}}%</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 长图容器 -->\r\n\t\t\t<scroll-view \r\n\t\t\t\tclass=\"long-scroll-container\"\r\n\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\t\t@scroll=\"onScroll\"\r\n\t\t\t\tenhanced\r\n\t\t\t\t:show-scrollbar=\"false\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"content-container\">\r\n\t\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t\t<view v-if=\"isLoading\" class=\"loading-container\">\r\n\t\t\t\t\t\t<view class=\"loading-icon\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-spinner fa-spin\"></i>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"loading-text\">正在加载协议内容...</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 协议图片列表 -->\r\n\t\t\t\t\t<view v-else class=\"images-container\">\r\n\t\t\t\t\t\t<!-- SVG文件显示 -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(image, index) in svgImages\"\r\n\t\t\t\t\t\t\t:key=\"`svg-${index}`\"\r\n\t\t\t\t\t\t\tclass=\"svg-container\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t:src=\"image.url\"\r\n\t\t\t\t\t\t\t\tmode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tclass=\"protocol-page svg-image\"\r\n\t\t\t\t\t\t\t\t:class=\"{'first-page': index === 0}\"\r\n\t\t\t\t\t\t\t\t@load=\"onImageLoad(image.originalIndex)\"\r\n\t\t\t\t\t\t\t\t@error=\"onImageError(image.originalIndex)\"\r\n\t\t\t\t\t\t\t\tlazy-load />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 普通图片显示 -->\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\tv-for=\"(image, index) in normalImages\"\r\n\t\t\t\t\t\t\t:key=\"`img-${index}`\"\r\n\t\t\t\t\t\t\t:src=\"image.url\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\"\r\n\t\t\t\t\t\t\tclass=\"protocol-page\"\r\n\t\t\t\t\t\t\t:class=\"{'first-page': index === 0}\"\r\n\t\t\t\t\t\t\t@load=\"onImageLoad(image.originalIndex)\"\r\n\t\t\t\t\t\t\t@error=\"onImageError(image.originalIndex)\"\r\n\t\t\t\t\t\t\tlazy-load />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 底部提示 -->\r\n\t\t\t\t\t<view class=\"bottom-tip\">\r\n\t\t\t\t\t\t<view class=\"tip-line\"></view>\r\n\t\t\t\t\t\t<text class=\"tip-text\">协议内容已全部加载完成</text>\r\n\t\t\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<button class=\"download-btn-small\" @click=\"downloadFile\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-download\"></i>\r\n\t\t\t\t\t\t\t\t下载协议\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t\r\n\t\t\t<!-- 阅读进度指示器 -->\r\n\t\t\t<view class=\"progress-indicator\">\r\n\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t<view class=\"progress-fill\" :style=\"{width: scrollProgress + '%'}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n\r\n// 接收的页面参数\r\nconst receivedFileUrl = ref('');\r\nconst receivedFileType = ref('');\r\nconst receivedFileName = ref('');\r\nconst receivedCaseNumber = ref('');\r\n\r\n// 文件信息（默认值，如果没有传递参数则使用）\r\nconst fileUrl = ref('http://*************:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx');\r\nconst pdfImagesBaseUrl = ref('http://*************:10010/pdf_images/scheme/485/');\r\nconst fileType = ref('pdf'); // 文件类型：pdf, svg, docx, etc.\r\nconst fileName = ref('调解协议.pdf');\r\n\r\n// 预览相关\r\nconst showImagePreview = ref(false);\r\nconst pdfImages = ref([]);\r\nconst isLoading = ref(false);\r\nconst loadedImages = ref(new Set());\r\n\r\n// 滚动相关\r\nconst scrollTop = ref(0);\r\nconst scrollProgress = ref(0);\r\nconst containerHeight = ref(0);\r\nconst contentHeight = ref(0);\r\n\r\n// 计算属性：分离SVG和普通图片\r\nconst svgImages = computed(() => {\r\n\treturn pdfImages.value\r\n\t\t.map((image, index) => ({ ...image, originalIndex: index }))\r\n\t\t.filter(image => image.type === 'svg');\r\n});\r\n\r\nconst normalImages = computed(() => {\r\n\treturn pdfImages.value\r\n\t\t.map((image, index) => ({ ...image, originalIndex: index }))\r\n\t\t.filter(image => !image.type || image.type !== 'svg');\r\n});\r\n\r\n/**\r\n * 图片预览模式\r\n */\r\nconst viewAsImages = async () => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tshowImagePreview.value = true;\r\n\t\t\r\n\t\t// 获取PDF转换的图片列表\r\n\t\tconst images = await getPdfImages();\r\n\t\tif (images.length > 0) {\r\n\t\t\tpdfImages.value = images;\r\n\t\t} else {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '暂无可预览内容',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\tcloseImagePreview();\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('加载图片预览失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败，请稍后重试',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t\tcloseImagePreview();\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 获取PDF转换的图片\r\n */\r\nconst getPdfImages = async () => {\r\n\t// 模拟加载延时\r\n\tawait new Promise(resolve => setTimeout(resolve, 1500));\r\n\t\r\n\t// 模拟图片列表（实际应该从服务器获取）\r\n\treturn [\r\n\t\t{ url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },\r\n\t\t{ url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },\r\n\t\t{ url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },\r\n\t\t{ url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },\r\n\t\t{ url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }\r\n\t];\r\n};\r\n\r\n/**\r\n * 滚动事件处理\r\n */\r\nconst onScroll = (e) => {\r\n\tconst { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;\r\n\t\r\n\t// 更新滚动位置\r\n\tscrollTop.value = currentScrollTop;\r\n\t\r\n\t// 计算阅读进度\r\n\tif (scrollHeight > clientHeight) {\r\n\t\tscrollProgress.value = (currentScrollTop / (scrollHeight - clientHeight)) * 100;\r\n\t}\r\n};\r\n\r\n/**\r\n * 图片加载成功\r\n */\r\nconst onImageLoad = (index) => {\r\n\tloadedImages.value.add(index);\r\n\tconsole.log(`图片 ${index + 1} 加载成功`);\r\n};\r\n\r\n/**\r\n * 图片加载失败\r\n */\r\nconst onImageError = (index) => {\r\n\tconsole.error(`图片 ${index + 1} 加载失败`);\r\n\tuni.showToast({\r\n\t\ttitle: `第${index + 1}页加载失败`,\r\n\t\ticon: 'none',\r\n\t\tduration: 2000\r\n\t});\r\n};\r\n\r\n/**\r\n * 关闭图片预览\r\n */\r\nconst closeImagePreview = () => {\r\n\tshowImagePreview.value = false;\r\n\tscrollTop.value = 0;\r\n\tscrollProgress.value = 0;\r\n\tloadedImages.value.clear();\r\n};\r\n\r\n/**\r\n * SVG文件预览\r\n */\r\nconst showSvgPreview = () => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tshowImagePreview.value = true;\r\n\r\n\t\t// 对于SVG文件，直接显示\r\n\t\tpdfImages.value = [{\r\n\t\t\turl: fileUrl.value,\r\n\t\t\tpage: 1,\r\n\t\t\ttype: 'svg'\r\n\t\t}];\r\n\r\n\t\tconsole.log('SVG文件预览准备完成:', fileUrl.value);\r\n\t} catch (error) {\r\n\t\tconsole.error('SVG预览失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: 'SVG预览失败',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 检测文件类型\r\n */\r\nconst detectFileType = (url) => {\r\n\tif (!url) return 'unknown';\r\n\r\n\tconst extension = url.split('.').pop()?.toLowerCase();\r\n\tswitch (extension) {\r\n\t\tcase 'svg':\r\n\t\t\treturn 'svg';\r\n\t\tcase 'pdf':\r\n\t\t\treturn 'pdf';\r\n\t\tcase 'docx':\r\n\t\tcase 'doc':\r\n\t\t\treturn 'docx';\r\n\t\tcase 'jpg':\r\n\t\tcase 'jpeg':\r\n\t\tcase 'png':\r\n\t\tcase 'gif':\r\n\t\t\treturn 'image';\r\n\t\tdefault:\r\n\t\t\treturn 'unknown';\r\n\t}\r\n};\r\n\r\n/**\r\n * 下载文件\r\n */\r\nconst downloadFile = () => {\r\n\tuni.showLoading({ title: '正在下载...' });\r\n\r\n\tuni.downloadFile({\r\n\t\turl: fileUrl.value,\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t// 对于SVG文件，可以直接保存到相册（如果是图片格式）\r\n\t\t\t\tif (fileType.value === 'svg') {\r\n\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\tconsole.error('保存SVG失败:', error);\r\n\t\t\t\t\t\t\t// 如果保存失败，尝试用文档方式打开\r\n\t\t\t\t\t\t\topenDocument(res.tempFilePath);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他文件类型用文档方式打开\r\n\t\t\t\t\topenDocument(res.tempFilePath);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: (error) => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tconsole.error('下载失败:', error);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '下载失败，请检查网络连接',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n/**\r\n * 打开文档\r\n */\r\nconst openDocument = (filePath) => {\r\n\tuni.openDocument({\r\n\t\tfilePath: filePath,\r\n\t\tshowMenu: true,\r\n\t\tsuccess: () => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '打开成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\tfail: (error) => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tconsole.error('打开文档失败:', error);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '打开失败，请检查是否安装相应的阅读器',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 页面加载时获取参数\r\nonLoad((options) => {\r\n\tconsole.log('预览页面参数:', options);\r\n\r\n\t// 获取文件URL参数\r\n\tif (options.fileUrl) {\r\n\t\ttry {\r\n\t\t\treceivedFileUrl.value = decodeURIComponent(options.fileUrl);\r\n\t\t\tfileUrl.value = receivedFileUrl.value;\r\n\t\t\tconsole.log('接收到文件URL:', fileUrl.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileUrl参数解码失败:', error);\r\n\t\t\treceivedFileUrl.value = options.fileUrl;\r\n\t\t\tfileUrl.value = options.fileUrl;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取文件类型参数\r\n\tif (options.fileType) {\r\n\t\ttry {\r\n\t\t\treceivedFileType.value = decodeURIComponent(options.fileType);\r\n\t\t\tfileType.value = receivedFileType.value;\r\n\t\t\tconsole.log('接收到文件类型:', fileType.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileType参数解码失败:', error);\r\n\t\t\treceivedFileType.value = options.fileType;\r\n\t\t\tfileType.value = options.fileType;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取文件名参数\r\n\tif (options.fileName) {\r\n\t\ttry {\r\n\t\t\treceivedFileName.value = decodeURIComponent(options.fileName);\r\n\t\t\tfileName.value = receivedFileName.value;\r\n\t\t\tconsole.log('接收到文件名:', fileName.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileName参数解码失败:', error);\r\n\t\t\treceivedFileName.value = options.fileName;\r\n\t\t\tfileName.value = options.fileName;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取案件号参数\r\n\tif (options.caseNumber) {\r\n\t\ttry {\r\n\t\t\treceivedCaseNumber.value = decodeURIComponent(options.caseNumber);\r\n\t\t\tconsole.log('接收到案件号:', receivedCaseNumber.value);\r\n\t\t\t// 根据案件号构建PDF图片基础URL\r\n\t\t\tpdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('caseNumber参数解码失败:', error);\r\n\t\t\treceivedCaseNumber.value = options.caseNumber;\r\n\t\t\tpdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${options.caseNumber}/`;\r\n\t\t}\r\n\t}\r\n});\r\n\r\nonMounted(() => {\r\n\t// 根据文件类型设置页面标题\r\n\tconst title = fileName.value ? `预览 - ${fileName.value}` : '文件预览';\r\n\tuni.setNavigationBarTitle({ title });\r\n\r\n\t// 根据文件类型选择预览方式\r\n\tif (fileType.value === 'svg') {\r\n\t\t// SVG文件直接显示\r\n\t\tshowSvgPreview();\r\n\t} else if (fileType.value === 'pdf' || fileType.value === 'docx') {\r\n\t\t// PDF或DOCX文件转图片预览\r\n\t\tviewAsImages();\r\n\t} else {\r\n\t\t// 其他文件类型尝试图片预览\r\n\t\tviewAsImages();\r\n\t}\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.protocol-preview-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.preview-options {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.option-btn {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #fff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 40rpx 20rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s ease;\r\n\t\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t.fas {\r\n\t\tfont-size: 48rpx;\r\n\t\tcolor: #3b7eeb;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\ttext {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.option-desc {\r\n\t\tfont-size: 24rpx !important;\r\n\t\tcolor: #666 !important;\r\n\t\tfont-weight: normal !important;\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n}\r\n\r\n/* 长图预览样式 */\r\n.long-image-preview {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100vw;\r\n\theight: 100vh;\r\n\tbackground-color: #fff;\r\n\tz-index: 9999;\r\n}\r\n\r\n.preview-header {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 0 30rpx;\r\n\tz-index: 10000;\r\n}\r\n\r\n/* .header-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.back-btn {\r\n\tbackground: transparent;\r\n\tborder: none;\r\n\tcolor: #333;\r\n\tfont-size: 32rpx;\r\n\tpadding: 8rpx;\r\n}\r\n\r\n.doc-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n} */\r\n\r\n.progress-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.long-scroll-container {\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\tpadding-top: 88rpx;\r\n}\r\n\r\n.content-container {\r\n\tmin-height: calc(100vh - 88rpx);\r\n}\r\n\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight: 60vh;\r\n\t\r\n\t.loading-icon {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.fas {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tcolor: #3b7eeb;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n}\r\n\r\n.images-container {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.protocol-page {\r\n\twidth: 100%;\r\n\tdisplay: block;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\r\n\t&.first-page {\r\n\t\tborder-top: none;\r\n\t}\r\n}\r\n\r\n/* SVG容器样式 */\r\n.svg-container {\r\n\twidth: 100%;\r\n\tbackground-color: #fff;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.svg-image {\r\n\twidth: 100%;\r\n\theight: auto;\r\n\tdisplay: block;\r\n}\r\n\r\n.bottom-tip {\r\n\tpadding: 60rpx 40rpx;\r\n\ttext-align: center;\r\n\tbackground-color: #f9f9f9;\r\n\t\r\n\t.tip-line {\r\n\t\twidth: 100rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground-color: #e0e0e0;\r\n\t\tmargin: 0 auto 30rpx;\r\n\t\tborder-radius: 2rpx;\r\n\t}\r\n\t\r\n\t.tip-text {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.download-btn-small {\r\n\tbackground-color: #3b7eeb;\r\n\tcolor: #fff;\r\n\tborder: none;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx 40rpx;\r\n\tfont-size: 28rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 12rpx;\r\n\t\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 6rpx;\r\n\tbackground-color: rgba(0, 0, 0, 0.1);\r\n\tz-index: 10000;\r\n}\r\n\r\n.progress-bar {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-fill {\r\n\theight: 100%;\r\n\tbackground: linear-gradient(90deg, #3b7eeb 0%, #2c62c9 100%);\r\n\ttransition: width 0.3s ease;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/protocol_preview/protocol_preview.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onLoad", "onMounted"], "mappings": ";;;;;AAqGA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,qBAAqBA,cAAAA,IAAI,EAAE;AAGjC,UAAM,UAAUA,cAAAA,IAAI,wGAAwG;AAC5H,UAAM,mBAAmBA,cAAAA,IAAI,mDAAmD;AAChF,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAC1B,UAAM,WAAWA,cAAAA,IAAI,UAAU;AAG/B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAG,IAAC,oBAAI,IAAG,CAAE;AAGlC,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AACJA,kBAAG,IAAC,CAAC;AACPA,kBAAG,IAAC,CAAC;AAG3B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAChC,aAAO,UAAU,MACf,IAAI,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO,eAAe,MAAK,EAAG,EAC1D,OAAO,WAAS,MAAM,SAAS,KAAK;AAAA,IACvC,CAAC;AAED,UAAM,eAAeA,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU,MACf,IAAI,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO,eAAe,MAAK,EAAG,EAC1D,OAAO,WAAS,CAAC,MAAM,QAAQ,MAAM,SAAS,KAAK;AAAA,IACtD,CAAC;AAKD,UAAM,eAAe,YAAY;AAChC,UAAI;AACH,kBAAU,QAAQ;AAClB,yBAAiB,QAAQ;AAGzB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,SAAS,GAAG;AACtB,oBAAU,QAAQ;AAAA,QACrB,OAAS;AACNC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;QACA;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;MACF,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,eAAe,YAAY;AAEhC,YAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,IAAI,CAAC;AAGtD,aAAO;AAAA,QACN,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,QACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,QACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,QACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,QACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,MACzD;AAAA,IACA;AAKA,UAAM,WAAW,CAAC,MAAM;AACvB,YAAM,EAAE,WAAW,kBAAkB,cAAc,aAAc,IAAG,EAAE;AAGtE,gBAAU,QAAQ;AAGlB,UAAI,eAAe,cAAc;AAChC,uBAAe,QAAS,oBAAoB,eAAe,gBAAiB;AAAA,MAC5E;AAAA,IACF;AAKA,UAAM,cAAc,CAAC,UAAU;AAC9B,mBAAa,MAAM,IAAI,KAAK;AAC5BA,0BAAA,MAAA,OAAA,sDAAY,MAAM,QAAQ,CAAC,OAAO;AAAA,IACnC;AAKA,UAAM,eAAe,CAAC,UAAU;AAC/BA,0BAAA,MAAA,SAAA,sDAAc,MAAM,QAAQ,CAAC,OAAO;AACpCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,IAAI,QAAQ,CAAC;AAAA,QACpB,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAE;AAAA,IACF;AAKA,UAAM,oBAAoB,MAAM;AAC/B,uBAAiB,QAAQ;AACzB,gBAAU,QAAQ;AAClB,qBAAe,QAAQ;AACvB,mBAAa,MAAM;IACpB;AAKA,UAAM,iBAAiB,MAAM;AAC5B,UAAI;AACH,kBAAU,QAAQ;AAClB,yBAAiB,QAAQ;AAGzB,kBAAU,QAAQ,CAAC;AAAA,UAClB,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,QACT,CAAG;AAEDA,sBAAA,MAAA,MAAA,OAAA,sDAAY,gBAAgB,QAAQ,KAAK;AAAA,MACzC,SAAQ,OAAO;AACfA,iGAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AA8BA,UAAM,eAAe,MAAM;AAC1BA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AAEpCA,oBAAAA,MAAI,aAAa;AAAA,QAChB,KAAK,QAAQ;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,eAAe,KAAK;AAE3B,gBAAI,SAAS,UAAU,OAAO;AAC7BA,4BAAAA,MAAI,uBAAuB;AAAA,gBAC1B,UAAU,IAAI;AAAA,gBACd,SAAS,MAAM;AACdA,gCAAG,MAAC,YAAW;AACfA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACd,CAAQ;AAAA,gBACD;AAAA,gBACD,MAAM,CAAC,UAAU;AAChBA,gCAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAE/B,+BAAa,IAAI,YAAY;AAAA,gBAC7B;AAAA,cACP,CAAM;AAAA,YACN,OAAW;AAEN,2BAAa,IAAI,YAAY;AAAA,YAC7B;AAAA,UACD;AAAA,QACD;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,sDAAc,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAKA,UAAM,eAAe,CAAC,aAAa;AAClCA,oBAAAA,MAAI,aAAa;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,QACV,SAAS,MAAM;AACdA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAG,MAAC,YAAW;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACd,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGAC,kBAAM,OAAC,CAAC,YAAY;AACnBD,oBAAA,MAAA,MAAA,OAAA,sDAAY,WAAW,OAAO;AAG9B,UAAI,QAAQ,SAAS;AACpB,YAAI;AACH,0BAAgB,QAAQ,mBAAmB,QAAQ,OAAO;AAC1D,kBAAQ,QAAQ,gBAAgB;AAChCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,aAAa,QAAQ,KAAK;AAAA,QACtC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,kBAAkB,KAAK;AACrC,0BAAgB,QAAQ,QAAQ;AAChC,kBAAQ,QAAQ,QAAQ;AAAA,QACxB;AAAA,MACD;AAGD,UAAI,QAAQ,UAAU;AACrB,YAAI;AACH,2BAAiB,QAAQ,mBAAmB,QAAQ,QAAQ;AAC5D,mBAAS,QAAQ,iBAAiB;AAClCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,YAAY,SAAS,KAAK;AAAA,QACtC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,mBAAmB,KAAK;AACtC,2BAAiB,QAAQ,QAAQ;AACjC,mBAAS,QAAQ,QAAQ;AAAA,QACzB;AAAA,MACD;AAGD,UAAI,QAAQ,UAAU;AACrB,YAAI;AACH,2BAAiB,QAAQ,mBAAmB,QAAQ,QAAQ;AAC5D,mBAAS,QAAQ,iBAAiB;AAClCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,WAAW,SAAS,KAAK;AAAA,QACrC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,mBAAmB,KAAK;AACtC,2BAAiB,QAAQ,QAAQ;AACjC,mBAAS,QAAQ,QAAQ;AAAA,QACzB;AAAA,MACD;AAGD,UAAI,QAAQ,YAAY;AACvB,YAAI;AACH,6BAAmB,QAAQ,mBAAmB,QAAQ,UAAU;AAChEA,wBAAA,MAAA,MAAA,OAAA,sDAAY,WAAW,mBAAmB,KAAK;AAE/C,2BAAiB,QAAQ,gDAAgD,mBAAmB,KAAK;AAAA,QACjG,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,qBAAqB,KAAK;AACxC,6BAAmB,QAAQ,QAAQ;AACnC,2BAAiB,QAAQ,gDAAgD,QAAQ,UAAU;AAAA,QAC3F;AAAA,MACD;AAAA,IACF,CAAC;AAEDE,kBAAAA,UAAU,MAAM;AAEf,YAAM,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK,KAAK;AAC1DF,oBAAAA,MAAI,sBAAsB,EAAE,MAAK,CAAE;AAGnC,UAAI,SAAS,UAAU,OAAO;AAE7B;MACF,WAAY,SAAS,UAAU,SAAS,SAAS,UAAU,QAAQ;AAEjE;MACF,OAAQ;AAEN;MACA;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzaD,GAAG,WAAW,eAAe;"}