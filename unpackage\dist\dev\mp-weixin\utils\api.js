"use strict";
const common_vendor = require("../common/vendor.js");
const server_require = require("../server/require.js");
const utils_userStore = require("./user-store.js");
const requestWithoutAuth = server_require.createRequest({ skipAuth: true });
const USER_API = {
  // 基础用户操作
  INFO: "/user/user_info/",
  LOGIN: "/user/login",
  UPDATE: "/user/update",
  BIND_PHONE: "/user/bind-phone",
  SEND_CODE: "/user/send-code",
  PHONE_LOGIN: "/user/phone-login",
  // 用户操作日志
  OPERATION_LOG: "/user/operation_log/"
};
const WECHAT_API = {
  LOGIN: "/wechat/login/",
  REFRESH: "/wechat/refresh/",
  BIND: "/wechat/bind/",
  UNBIND: "/wechat/unbind/",
  USER_INFO: "/wechat/userinfo/",
  FACEID_AUTH: "/wechat/faceid/auth/"
  // 人脸核身认证接口
};
const HOME_API = {
  GRID_DATA: "/home/<USER>"
};
const MEDIATION_QUERY_API = {
  LIST: "/mediation_management/mediation_case/wechat/list/",
  DETAIL: "/mediation_management/mediation_case",
  CASE_BY_NUMBER: "/mediation_management/mediation_case/",
  CASE_COUNT_BY_IDENTITY: "/mediation_management/mediation_case/wechat/by_debtor/"
};
const WORK_ORDER_API = {
  DETAIL: "/mediation_management/mediation_case",
  ACCEPT: "/mediation_management/mediation_case/wechat",
  REJECT: "/work-order/reject"
};
const SOLUTION_API = {
  DETAIL: "/solution/detail",
  CONFIRM: "/mediation_management/mediation_case/wechat",
  ADJUST: "/solution/adjust"
};
const DEBT_CONFIRM_API = {
  LIST: "/debt-confirm/list",
  DETAIL: "/debt-confirm/detail",
  SUBMIT: "/debt-confirm/submit"
};
const MEDIATION_COMPLAINT_API = {
  LIST: "/mediation-complaint/list",
  DETAIL: "/mediation-complaint/detail",
  SUBMIT: "/mediation-complaint/submit"
};
const REAL_CASE_API = {
  LIST: "/case_display/case_display/",
  DETAIL: "/real-case/detail",
  SUBMIT: "/user/files/download/8de29944-dd25-40ad-a5b9-c581ba9d3f5e/"
};
const FEEDBACK_API = {
  SUBMIT: "/feedback/submit",
  HISTORY: "/feedback/history"
};
const getApiPath = {
  // 获取用户详情
  userDetail: (id) => `${USER_API.DETAIL}/${id}`,
  // 获取调解查询详情==》调解确认（调解信息、相关文件）
  mediationQueryDetail: (id) => `${MEDIATION_QUERY_API.DETAIL}/${id}/content/`,
  // 获取调解查询详情==》（获取单条调解数据基本信息：案件号、状态、日期）
  mediationSingleDetail: (id) => `${MEDIATION_QUERY_API.LIST}/${id}`,
  // 获取待确认案件信息详情
  workOrderDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/content/`,
  // 获取进行中方案信息详情
  workPlanDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/plan_config/`,
  // 获取调解查询详情==》调解确认（接受调解）
  workOrderAccept: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/confirm_status/`,
  // 获取调解查询详情==》已完成
  workOrderCompleted: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/detail/`,
  // 获取拒绝
  workOrderReject: (id) => `${WORK_ORDER_API.REJECT}/${id}`,
  // 获取调解方案详情
  solutionDetail: (orderId) => `${SOLUTION_API.DETAIL}/${orderId}`,
  // 获取调解方案确认
  solutionConfirm: (mediationPlan) => `${SOLUTION_API.CONFIRM}/${mediationPlan}/update_mediation_plan/`,
  // 获取调解方案调整
  solutionAdjust: (orderId) => `${SOLUTION_API.ADJUST}/${orderId}`,
  // 获取债权确认详情
  debtConfirmDetail: (id) => `${DEBT_CONFIRM_API.DETAIL}/${id}`,
  // 获取调解投诉详情
  mediationComplaintDetail: (id) => `${MEDIATION_COMPLAINT_API.DETAIL}/${id}`,
  // 获取案例详情
  realCaseDetail: (id) => `${REAL_CASE_API.DETAIL}/${id}`,
  realCase: () => `${REAL_CASE_API.SUBMIT}`
};
const api = {
  // Token管理工具 - 使用server/require.js中的authUtils
  auth: server_require.authUtils,
  // 用户相关
  user: {
    // 获取用户信息
    getUserInfo: () => {
      return server_require.request({
        url: USER_API.INFO,
        method: "GET"
      });
    },
    // 普通登录 - 增强版本，支持登录成功后自动存储认证信息
    login: async (data) => {
      try {
        const response = await requestWithoutAuth({
          url: USER_API.LOGIN,
          method: "POST",
          data
        });
        if (response && response.success !== false) {
          const loginData = response.data || response;
          if (loginData.access_token && loginData.token_type) {
            server_require.authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            server_require.authUtils.setToken(loginData.token);
          }
          if (loginData.refresh_token) {
            server_require.authUtils.setRefreshToken(loginData.refresh_token);
          }
          if (loginData.openid) {
            common_vendor.index.setStorageSync("openid", loginData.openid);
          }
          if (loginData.unionid) {
            common_vendor.index.setStorageSync("unionid", loginData.unionid);
          }
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            utils_userStore.userStore.setUserInfo(userInfo);
          }
          if (loginData.wechat_userInfo || loginData.wechatInfo) {
            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;
            utils_userStore.userStore.setWechatUserInfo(wechatInfo);
          }
          common_vendor.index.__f__("log", "at utils/api.js:264", "登录成功，认证信息已自动存储");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/api.js:269", "登录失败:", error);
        throw error;
      }
    },
    // 更新用户信息
    updateUserInfo: (data) => {
      return server_require.request({
        url: USER_API.UPDATE,
        method: "PUT",
        data
      });
    },
    // 绑定手机号
    bindPhone: (data) => {
      return server_require.request({
        url: USER_API.BIND_PHONE,
        method: "POST",
        data
      });
    },
    // 发送验证码（跳过Authorization头部）
    sendVerifyCode: (data) => {
      return requestWithoutAuth({
        url: USER_API.SEND_CODE,
        method: "POST",
        data
      });
    },
    // 手机号登录 - 增强版本，支持登录成功后自动存储认证信息
    phoneLogin: async (data) => {
      try {
        const response = await requestWithoutAuth({
          url: USER_API.PHONE_LOGIN,
          method: "POST",
          data
        });
        if (response && response.success !== false) {
          const loginData = response.data || response;
          if (loginData.access_token && loginData.token_type) {
            server_require.authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            server_require.authUtils.setToken(loginData.token);
          }
          if (loginData.refresh_token) {
            server_require.authUtils.setRefreshToken(loginData.refresh_token);
          }
          if (loginData.openid) {
            common_vendor.index.setStorageSync("openid", loginData.openid);
          }
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            utils_userStore.userStore.setUserInfo(userInfo);
          }
          common_vendor.index.__f__("log", "at utils/api.js:344", "手机号登录成功，认证信息已自动存储");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/api.js:349", "手机号登录失败:", error);
        throw error;
      }
    }
  },
  // 微信认证相关
  wechat: {
    // 微信登录 - 增强版本，支持登录成功后自动存储认证信息
    login: async (data) => {
      try {
        const response = await requestWithoutAuth({
          url: WECHAT_API.LOGIN,
          method: "POST",
          data
        });
        if (response && response.success !== false) {
          const loginData = response.data || response;
          if (loginData.access_token && loginData.token_type) {
            server_require.authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            server_require.authUtils.setToken(loginData.token);
          }
          if (loginData.refresh_token) {
            server_require.authUtils.setRefreshToken(loginData.refresh_token);
          }
          if (loginData.openid) {
            common_vendor.index.setStorageSync("openid", loginData.openid);
          }
          if (loginData.unionid) {
            common_vendor.index.setStorageSync("unionid", loginData.unionid);
          }
          if (loginData.sessionKey || loginData.session_key) {
            common_vendor.index.setStorageSync("sessionKey", loginData.sessionKey || loginData.session_key);
          }
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            utils_userStore.userStore.setUserInfo(userInfo);
          }
          if (loginData.wechat_userInfo || loginData.wechatInfo) {
            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;
            utils_userStore.userStore.setWechatUserInfo(wechatInfo);
          }
          common_vendor.index.__f__("log", "at utils/api.js:416", "微信登录成功，认证信息已自动存储");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/api.js:421", "微信登录失败:", error);
        throw error;
      }
    },
    // 微信token刷新（使用refresh_token，跳过Authorization头部）
    refresh: (data) => {
      return requestWithoutAuth({
        url: WECHAT_API.REFRESH,
        method: "POST",
        data
      });
    },
    // 绑定微信
    bind: (data) => {
      return server_require.request({
        url: WECHAT_API.BIND,
        method: "POST",
        data
      });
    },
    // 解绑微信
    unbind: () => {
      return server_require.request({
        url: WECHAT_API.UNBIND,
        method: "POST"
      });
    },
    // 获取微信用户信息
    getUserInfo: (data) => {
      return server_require.request({
        url: WECHAT_API.USER_INFO,
        method: "POST",
        data
      });
    },
    // 人脸核身认证
    faceIdAuth: (data) => {
      return server_require.request({
        url: WECHAT_API.FACEID_AUTH,
        method: "POST",
        data
      });
    }
  },
  // 首页相关
  home: {
    // 获取首页卡片数据（公开数据，跳过Authorization头部）
    getGridData: () => {
      return requestWithoutAuth({
        url: HOME_API.GRID_DATA,
        method: "GET"
      });
    }
  },
  // 调解查询
  mediationQuery: {
    // 获取调解查询列表
    getAuthenticatedList: (params) => {
      return server_require.request({
        url: MEDIATION_QUERY_API.LIST,
        method: "GET",
        data: params
      });
    },
    // 获取调解查询详情
    getDetail: (id) => {
      return server_require.request({
        url: getApiPath.mediationQueryDetail(id),
        method: "GET"
      });
    },
    // 获取单条调解数据基本信息（案件号、状态、日期）
    getSingleDetail: (id) => {
      return server_require.request({
        url: getApiPath.mediationSingleDetail(id),
        method: "GET"
      });
    },
    // 根据调解案件号查询调解案件
    getCaseByNumber: (caseNumber) => {
      return server_require.request({
        url: MEDIATION_QUERY_API.CASE_BY_NUMBER,
        method: "GET",
        data: {
          case_number: caseNumber
        }
      });
    },
    // 根据身份信息查询调解案件数量
    getCaseCountByIdentity: (params) => {
      return server_require.request({
        url: MEDIATION_QUERY_API.CASE_COUNT_BY_IDENTITY,
        method: "GET",
        data: params
      });
    }
  },
  // 调解信息相关
  workOrder: {
    // 获取详情
    getDetail: (id) => {
      return server_require.request({
        url: getApiPath.workOrderDetail(id),
        method: "GET"
      });
    },
    // 接受调解
    acceptWorkOrder: (id) => {
      return server_require.request({
        url: getApiPath.workOrderAccept(id),
        method: "PUT"
      });
    },
    // 获取已完成的调解信息，还款方案
    getCompletedWorkOrder: (id) => {
      return server_require.request({
        url: getApiPath.workOrderCompleted(id),
        method: "GET"
      });
    }
    // 拒绝
    /* rejectWorkOrder: (id, reason) => {
      return request({
        url: getApiPath.workOrderReject(id),
        method: 'POST',
        data: { reason }
      });
    } */
  },
  // 调解方案相关
  solution: {
    // 获取调解方案详情
    getDetail: (orderId) => {
      return server_require.request({
        url: getApiPath.solutionDetail(orderId),
        method: "GET"
      });
    },
    // 获取工作方案详情配置数据
    getPlanDetail: (id) => {
      return server_require.request({
        url: getApiPath.workPlanDetail(id),
        method: "GET"
      });
    },
    // 确认调解方案
    confirmSolution: (orderId) => {
      return server_require.request({
        url: getApiPath.solutionConfirm(orderId),
        method: "POST"
      });
    },
    // 申请调整方案
    adjustSolution: (orderId, data) => {
      return server_require.request({
        url: getApiPath.solutionAdjust(orderId),
        method: "POST",
        data
      });
    }
  },
  // 债权确认
  debtConfirm: {
    // 获取债权确认列表
    getList: (params) => {
      return server_require.request({
        url: DEBT_CONFIRM_API.LIST,
        method: "GET",
        data: params
      });
    },
    // 获取债权确认详情
    getDetail: (id) => {
      return server_require.request({
        url: getApiPath.debtConfirmDetail(id),
        method: "GET"
      });
    },
    // 提交债权确认
    submit: (data) => {
      return server_require.request({
        url: DEBT_CONFIRM_API.SUBMIT,
        method: "POST",
        data
      });
    }
  },
  // 调解投诉
  mediationComplaint: {
    // 获取投诉列表
    getList: (params) => {
      return server_require.request({
        url: MEDIATION_COMPLAINT_API.LIST,
        method: "GET",
        data: params
      });
    },
    // 获取投诉详情
    getDetail: (id) => {
      return server_require.request({
        url: getApiPath.mediationComplaintDetail(id),
        method: "GET"
      });
    },
    // 提交投诉
    submit: (data) => {
      return server_require.request({
        url: MEDIATION_COMPLAINT_API.SUBMIT,
        method: "POST",
        data
      });
    }
  },
  // 案例展示
  realCase: {
    // 获取案例列表（公开数据，跳过Authorization头部）
    getList: (params) => {
      return requestWithoutAuth({
        url: REAL_CASE_API.LIST,
        method: "GET",
        data: params
      });
    },
    // 获取案例详情（公开数据，跳过Authorization头部）
    getDetail: (id) => {
      return requestWithoutAuth({
        url: getApiPath.realCaseDetail(id),
        method: "GET"
      });
    },
    getDetails: () => {
      return server_require.request({
        url: getApiPath.realCase(),
        method: "GET"
      });
    }
    /* getSingleDetail: (id) => {
      return request({
        url: getApiPath.mediationSingleDetail(id),
        method: 'GET'
      });
    }, */
  },
  // 用户操作日志 - 支持操作记录功能
  operationLog: {
    // 记录用户操作
    recordOperation: (data) => {
      return server_require.request({
        url: USER_API.OPERATION_LOG,
        method: "POST",
        data
      }).catch((error) => {
        common_vendor.index.__f__("warn", "at utils/api.js:702", "操作日志记录失败:", error.message);
        return Promise.resolve();
      });
    }
  },
  // 意见反馈
  feedback: {
    // 提交反馈
    submitFeedback: (data) => {
      return server_require.request({
        url: FEEDBACK_API.SUBMIT,
        method: "POST",
        data
      });
    },
    // 获取反馈历史
    getFeedbackHistory: () => {
      return server_require.request({
        url: FEEDBACK_API.HISTORY,
        method: "GET"
      });
    }
  }
};
exports.api = api;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/api.js.map
